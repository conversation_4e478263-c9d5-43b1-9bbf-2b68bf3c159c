package com.sartorua.thrust_tester

import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityTest {

    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Test
    fun testActivityLaunches() {
        // This test simply verifies that the MainActivity can be launched
        // without crashing, which is the main issue we're trying to solve
        activityRule.scenario.onActivity { activity ->
            // Activity launched successfully if we reach this point
            assert(activity != null)
        }
    }
}
